spring.application.name=order-history
spring.profiles.active=dev

server.port=8040

management.endpoint.health.enabled=true
management.endpoints.web.exposure.include=*
management.endpoint.health.show-details=always
management.endpoint.health.probes.enabled=true
management.endpoint.loggers.enabled=true
management.health.livenessState.enabled=true
management.health.readinessState.enabled=true
management.metrics.tags.wyden_service=order-history
management.endpoint.health.group.liveness.include=livenessState,rabbit,diskSpace
management.endpoint.health.group.readiness.include=readinessState

rabbitmq.username = order-history
rabbitmq.password = password
rabbitmq.virtualHost = /
rabbitmq.host = localhost
# default RabbitMQ port for non-TLS connections: 5672, default port for TLS connections: 5671
rabbitmq.port = 5672
# specify a valid protocol name, e.g. "TLSv1.2" . leave empty for non-TLS connection
rabbitmq.tls =

# Trading queue
rabbitmq.trading-order-history-queue = order-history-queue.trading.ALL
rabbitmq.trading-order-history-queue-simplified = order-history-queue.trading.responses

db.engine=psql
database.host=localhost
database.name=order_history
spring.datasource.url=jdbc:postgresql://${database.host}:5432/${database.name}
spring.datasource.username= order_history
spring.datasource.password= password

spring.flyway.url=${spring.datasource.url}
spring.flyway.user=${spring.datasource.username}
spring.flyway.password=${spring.datasource.password}
spring.flyway.enabled=true
spring.flyway.locations=classpath:psql/migration/schema,classpath:psql/migration/data

tracing.collector.endpoint=http://localhost:4317

hz.addressList=localhost
hz.outboundPortDefinition=