package io.wyden.orderhistory.service;

import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.orderhistory.model.OrderHistorySearchIndex;
import io.wyden.orderhistory.repository.OrderEventRepository;
import io.wyden.orderhistory.repository.OrderHistorySearchIndexRepository;
import io.wyden.orderhistory.service.outbound.OrderStateEmitter;
import io.wyden.orderhistory.service.utils.OrderStateMapperExtensions;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
public class AsyncOrderStateProcessor {

    private static final Logger LOGGER = LoggerFactory.getLogger(AsyncOrderStateProcessor.class);

    private final ExecutorService virtualThreadExecutor;
    private final OrderEventRepository orderEventRepository;
    private final OrderHistorySearchIndexRepository searchIndexRepository;
    private final OrderStateEmitter orderStateEmitter;

    public AsyncOrderStateProcessor(
            OrderEventRepository orderEventRepository,
            OrderHistorySearchIndexRepository searchIndexRepository,
            OrderStateEmitter orderStateEmitter) {
        this.virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
        this.orderEventRepository = orderEventRepository;
        this.searchIndexRepository = searchIndexRepository;
        this.orderStateEmitter = orderStateEmitter;
    }

    @Transactional
    public void scheduleProcessing(OrderEventEntity orderEvent) {
        virtualThreadExecutor.submit(() -> processEvent(orderEvent));
    }

    protected void processEvent(OrderEventEntity orderEvent) {
        try {
            // Check if this event is newer than current search index
            Optional<OrderHistorySearchIndex> currentIndex = searchIndexRepository.findByOrderId(orderEvent.getOrderId());

            if (currentIndex.isPresent() && !orderEvent.getMessageTimestamp().isAfter(currentIndex.get().getLatestMessageTimestamp())) {
                LOGGER.debug("Skipping older event {} for order {} (timestamp: {})", orderEvent, orderEvent.getOrderId(), orderEvent.getMessageTimestamp());
                return;
            }

            // Build OrderState from Response (with embedded Request)
            Message message = deserializeProto(orderEvent.getProtoBlob(), orderEvent.getMessageType());
            OrderState orderState = buildOrderStateFromResponse(message);

            // Update search index
            updateSearchIndex(orderEvent.getOrderId(), orderState, orderEvent);

            // Emit to RabbitMQ
            orderStateEmitter.emit(orderState);

            LOGGER.debug("Successfully processed event {} for order {}", orderEvent, orderEvent.getOrderId());
        } catch (Exception e) {
            handleProcessingError(orderEvent, e);
        }
    }

    private Message deserializeProto(byte[] protoBlob, String messageType) throws InvalidProtocolBufferException {
        return switch (messageType) {
            case "OemsResponse" -> OemsResponse.parseFrom(protoBlob);
            case "ClientResponse" -> ClientResponse.parseFrom(protoBlob);
            default -> throw new IllegalArgumentException("Unsupported message type: " + messageType);
        };
    }

    private OrderState buildOrderStateFromResponse(Message response) {
        if (response instanceof OemsResponse oemsResponse) {
            return applyOemsResponseOnRequest(oemsResponse.getRequest(), oemsResponse);
        } else if (response instanceof ClientResponse clientResponse) {
            return applyClientResponseOnRequest(clientResponse.getRequest(), clientResponse);
        }
        throw new IllegalArgumentException("Unsupported message type: " + response.getClass());
    }

    private OrderState applyOemsResponseOnRequest(io.wyden.published.oems.OemsRequest request, OemsResponse response) {
        // Use existing OrderStateMapper logic but build from Request + Response
        // This is a simplified version - you'll need to implement the full mapping logic
        return OrderStateMapperExtensions.buildFromOemsRequestAndResponse(request, response);
    }

    private OrderState applyClientResponseOnRequest(io.wyden.published.client.ClientRequest request, ClientResponse response) {
        // Use existing OrderStateMapper logic but build from Request + Response
        // This is a simplified version - you'll need to implement the full mapping logic
        return OrderStateMapperExtensions.buildFromClientRequestAndResponse(request, response);
    }

    private void updateSearchIndex(String orderId, OrderState orderState, OrderEventEntity event) {
        OrderHistorySearchIndex index = buildSearchIndexFromOrderState(orderState);
        index.setLatestMessageTimestamp(event.getMessageTimestamp());
        index.setLatestEventId(event.getId());

        searchIndexRepository.save(index); // JPA handles upsert
        
        LOGGER.debug("Updated search index for order {}", orderId);
    }

    private OrderHistorySearchIndex buildSearchIndexFromOrderState(OrderState orderState) {
        return OrderHistorySearchIndex.builder()
            .orderId(orderState.getOrderId())
            .portfolioId(orderState.getPortfolioId())
            .instrumentId(orderState.getInstrumentId())
            .clOrderId(orderState.getClOrderId())
            .orderStatus(orderState.getOrderStatus().name())
            .orderCategory(orderState.getOrderCategory().name())
            .parentOrderId(orderState.getParentOrderId())
            .rootOrderId(orderState.getRootOrderId())
            .extOrderId(orderState.getExtOrderId())
            .createdAt(orderState.getCreatedAt() != null ? 
                java.time.OffsetDateTime.parse(orderState.getCreatedAt()) : null)
            .updatedAt(orderState.getUpdatedAt() != null ? 
                java.time.OffsetDateTime.parse(orderState.getUpdatedAt()) : null)
            .build();
    }

    private void handleProcessingError(OrderEventEntity event, Exception e) {
        LOGGER.error("Failed to process event {}: {}", event, e.getMessage(), e);
        
        // Here you could implement retry logic, dead letter queue, etc.
        // For now, we just log the error
        
        // TODO: Implement retry mechanism
        // TODO: Send to dead letter queue after max retries
        // TODO: Alert monitoring system
    }

    // Graceful shutdown
    public void shutdown() {
        LOGGER.info("Shutting down AsyncOrderStateProcessor...");
        virtualThreadExecutor.shutdown();
        try {
            if (!virtualThreadExecutor.awaitTermination(30, java.util.concurrent.TimeUnit.SECONDS)) {
                LOGGER.warn("Executor did not terminate gracefully, forcing shutdown");
                virtualThreadExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            LOGGER.warn("Interrupted while waiting for executor termination");
            virtualThreadExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
