package io.wyden.orderhistory.service;

import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.published.client.ClientRequest;
import io.wyden.published.client.ClientResponse;
import io.wyden.published.client.ClientResponseType;
import io.wyden.published.common.Metadata;
import io.wyden.published.oems.OemsRequest;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.oems.OemsResponse.OemsResponseType;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

class TradingMessageConsumerIntegrationTest extends TradingMessageConsumerIntegrationTestBase {

    @Test
    void shouldProcessValidOemsResponseAndSaveEvent() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());

        // Create a test OemsResponse
        OemsResponse response = createTestOemsResponse(UUID.randomUUID().toString());

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();

        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldProcessValidClientResponseAndSaveEvent() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());

        // Create a test ClientResponse
        ClientResponse response = createTestClientResponse(UUID.randomUUID().toString());

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();

        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        verify(mockAsyncOrderStateProcessor, times(1)).scheduleProcessing(any());
    }

    @Test
    void shouldHandleInvalidMessageGracefully() {
        // When
        OemsResponse responseWithoutRequest = createTestOemsResponse(UUID.randomUUID().toString()).toBuilder().clearRequest().build();
        tradingIngressExchange.publishWithHeaders(responseWithoutRequest, TRADING_HEADERS);

        // Then
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            // Verify no events were saved
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).noneMatch(event -> event.getOrderId().equalsIgnoreCase(responseWithoutRequest.getOrderId()));
        });

        // No processing should be scheduled
        verify(mockAsyncOrderStateProcessor, times(0)).scheduleProcessing(any());
    }

    @Test
    void shouldNotProcessMessagesNotRequiringProcessing() {
        // Given
        OemsResponse response = createNonProcessableOemsResponse();

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            // Verify event were not saved
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).noneMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });

        // No processing should be scheduled
        verify(mockAsyncOrderStateProcessor, times(0)).scheduleProcessing(any());
    }

    private OemsResponse createTestOemsResponse(String orderId) {
        return OemsResponse.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(orderId)
            .setResponseType(OemsResponseType.EXECUTION_REPORT)
            .setRequest(OemsRequest.newBuilder()
                .setMetadata(Metadata.newBuilder()
                    .setRequestId(UUID.randomUUID().toString())
                    .build())
                .setOrderId(orderId)
                .build())
            .build();
    }

    private ClientResponse createTestClientResponse(String orderId) {
        return ClientResponse.newBuilder()
            .setMetadata(Metadata.newBuilder()
                .setRequestId(UUID.randomUUID().toString())
                .build())
            .setOrderId(orderId)
            .setResponseType(ClientResponseType.EXECUTION_REPORT)
            .setRequest(ClientRequest.newBuilder()
                .setMetadata(Metadata.newBuilder()
                    .setRequestId(UUID.randomUUID().toString())
                    .build())
                .setOrderId(orderId)
                .build())
            .build();
    }

    private OemsResponse createNonProcessableOemsResponse() {
        return OemsResponse.newBuilder()
            .setResponseType(OemsResponseType.RESPONSE_TYPE_UNSPECIFIED)
            .build();
    }
}
