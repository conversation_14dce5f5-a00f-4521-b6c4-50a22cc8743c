package io.wyden.orderhistory.service;

import io.wyden.orderhistory.infrastructure.rabbit.SimplifiedTradingMessageConsumer;
import io.wyden.orderhistory.model.OrderEventEntity;
import io.wyden.published.oems.OemsResponse;
import io.wyden.published.reporting.OrderState;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doAnswer;
import static org.testcontainers.shaded.org.awaitility.Awaitility.await;

public class AsyncOrderStateProcessorIntegrationTest extends TradingMessageConsumerIntegrationTestBase {

    @Autowired
    AsyncOrderStateProcessor asyncOrderStateProcessor;

    private final BlockingQueue<OrderState> consumerOrderStateQueue = new LinkedBlockingQueue<>();
    private String orderStateTag;

    @BeforeEach
    public void setUp() {
        consumer = new SimplifiedTradingMessageConsumer(
            rabbitIntegrator,
            rabbitDestinations.tradingIngressExchange(rabbitIntegrator),
            orderEventRepository,
            asyncOrderStateProcessor,
            telemetry.getTracing(),
            queueName,
            "async-order-state-processor-test-consumer");
    }

    @Test
    void shouldProcessValidOemsResponseAndSaveEvent() throws Exception {
        // Given
        CountDownLatch latch = new CountDownLatch(1);
        doAnswer(invocation -> {
            latch.countDown();
            return null;
        }).when(mockAsyncOrderStateProcessor).scheduleProcessing(any());

        // Create a test OemsResponse
        OemsResponse response = createTestOemsResponse(UUID.randomUUID().toString());

        // When
        tradingIngressExchange.publishWithHeaders(response, TRADING_HEADERS);

        // Then
        boolean processed = latch.await(5, TimeUnit.SECONDS);
        assertThat(processed).isTrue();

        await().atMost(5, TimeUnit.SECONDS).untilAsserted(() -> {
            List<OrderEventEntity> events = orderEventRepository.findAll();
            assertThat(events).isNotEmpty();
            assertThat(events).anyMatch(event -> event.getOrderId().equalsIgnoreCase(response.getOrderId()));
        });
    }
}
