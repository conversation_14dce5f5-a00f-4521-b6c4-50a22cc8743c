# Order History Service FileStructure

## Architecture Layers

```
src/main/java/io/wyden/orderhistory/ 
├── web/
│ └── OrderHistoryController.java 
├── service/ 
│ ├── OrderHistoryService.java 
│ └── OrderStateProcessor.java 
├── repository/ 
│ 
│ 
├── OrderStateRepository.java │ ├── PostgresOrderStateRepository.java │ ├── MeteredPostgresOrderStateRepository.java │ ├── PortfolioProvider.java │ └── RepositoryConfig.java ├── model/ │ ├── OrderStateEntity.java │ ├── CancelReplaceRequestEntity.java │ ├── OrderHistorySearchInput.java │ ├── SimplePredicateInput.java │ ├── CollectionPredicateInput.java │ ├── DatePredicateInput.java │ ├── PredicateInput.java │ └── SortingOrder.java ├── infrastructure/ │ ├── hazelcast/ │ │ └── HazelcastConfig.java │ ├── health/ │ │ ├── HealthConfiguration.java │ │ └── HealthIndicators.java │ ├── rabbit/ │ │ ├── TradingMessageConsumer.java │ │ ├── RabbitDestinations.java │ │ ├── RabbitHealthCheckConfiguration.java │ │ └── RabbitWiring.java │ └── telemetry/ │ ├── Meters.java │ ├── MetricsConfiguration.java │ └── TelemetryConfiguration.java └── config/ ├── DatabaseConfig.java ├── RabbitMQConfig.java └── ApplicationConfig.java
```
